import React from 'react';

interface FileUploadButtonProps {
  onFileSelect: (file: File) => void;
  icon: React.ReactNode;
  accept?: string;
  className?: string;
}

const DEFAULT_ACCEPT = '.jpeg,.jpg,.png,.docx,.pdf,.txt,.csv';

const FileUploadButton: React.FC<FileUploadButtonProps> = ({
  onFileSelect,
  icon,
  accept = DEFAULT_ACCEPT,
  className = '',
}) => {
  return (
    <label className={`cursor-pointer ${className}`}>
      {icon}
      <input
        type="file"
        accept={accept}
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            const allowedTypes = [
              'image/jpeg',
              'image/jpg', 
              'image/png',
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
              'application/pdf',
              'text/plain', // TXT
              'text/csv' // CSV
            ];
            if (!allowedTypes.includes(file.type)) {
              alert('Only JPEG, JPG, PNG, DOCX, PDF, TXT, or CSV files are allowed.');
              e.target.value = '';
              return;
            }
            onFileSelect(file);
          }
        }}
      />
    </label>
  );
};

export default FileUploadButton;
